<?php
// Create a simple video placeholder image
$width = 400;
$height = 400;

// Create image
$image = imagecreatetruecolor($width, $height);

// Define colors
$purple = imagecolorallocate($image, 138, 43, 226); // Purple gradient color
$white = imagecolorallocate($image, 255, 255, 255);
$gray = imagecolorallocate($image, 200, 200, 200);

// Fill background with gradient-like purple
imagefill($image, 0, 0, $purple);

// Add a play button icon (triangle)
$play_size = 60;
$center_x = $width / 2;
$center_y = $height / 2;

// Create play button triangle
$triangle = array(
    $center_x - $play_size/2, $center_y - $play_size/2,  // Top left
    $center_x - $play_size/2, $center_y + $play_size/2,  // Bottom left
    $center_x + $play_size/2, $center_y                  // Right point
);

imagefilledpolygon($image, $triangle, 3, $white);

// Add text
$font_size = 5;
$text = "VIDEO";
$text_width = imagefontwidth($font_size) * strlen($text);
$text_x = ($width - $text_width) / 2;
$text_y = $center_y + $play_size;

imagestring($image, $font_size, $text_x, $text_y, $text, $white);

// Save image
$output_path = __DIR__ . '/assets/images/video-placeholder.jpg';
imagejpeg($image, $output_path, 90);

// Clean up
imagedestroy($image);

echo "Placeholder image created at: " . $output_path . "\n";
?>
