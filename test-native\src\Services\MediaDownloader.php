<?php
namespace Services;

class MediaDownloader {
    private $uploadDir;
    private $baseUrl;

    public function __construct($uploadDir = null, $baseUrl = null) {
        // Set default upload directory
        $this->uploadDir = $uploadDir ?: __DIR__ . '/../../../wp-content/uploads/instagram-media';
        $this->baseUrl = $baseUrl ?: '/wp-content/uploads/instagram-media';

        // Create upload directory if it doesn't exist
        if (!file_exists($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }

        // Create yearly and monthly directories
        $this->createDirectoryStructure();
    }

    private function createDirectoryStructure() {
        $year = date('Y');
        $month = date('m');

        $yearDir = $this->uploadDir . '/' . $year;
        $monthDir = $yearDir . '/' . $month;

        if (!file_exists($yearDir)) {
            mkdir($yearDir, 0755, true);
        }

        if (!file_exists($monthDir)) {
            mkdir($monthDir, 0755, true);
        }
    }

    public function downloadMedia($mediaUrl, $instagramId, $mediaType = 'image') {
        try {
            // Check if media for this Instagram ID already exists
            $existingFile = $this->findExistingFile($instagramId, $mediaType);
            if ($existingFile) {
                return [
                    'success' => true,
                    'local_url' => $existingFile['url'],
                    'local_path' => $existingFile['path'],
                    'file_exists' => true
                ];
            }

            // Generate unique filename
            $extension = $this->getFileExtension($mediaUrl, $mediaType);
            $filename = $instagramId . '_' . uniqid() . '.' . $extension;

            // Create yearly/monthly path
            $year = date('Y');
            $month = date('m');
            $relativePath = "$year/$month/$filename";
            $fullPath = $this->uploadDir . "/$year/$month/$filename";

            // Download the file
            $fileContent = $this->downloadFile($mediaUrl);

            if ($fileContent === false) {
                return [
                    'success' => false,
                    'error' => 'Failed to download file'
                ];
            }

            // Save the file
            if (file_put_contents($fullPath, $fileContent) === false) {
                return [
                    'success' => false,
                    'error' => 'Failed to save file'
                ];
            }

            return [
                'success' => true,
                'local_url' => $this->baseUrl . '/' . $relativePath,
                'local_path' => $fullPath,
                'file_exists' => false
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    public function downloadThumbnail($thumbnailUrl, $instagramId) {
        try {
            // Check if thumbnail for this Instagram ID already exists
            $existingThumbnail = $this->findExistingFile($instagramId, 'image', true);
            if ($existingThumbnail) {
                return [
                    'success' => true,
                    'local_url' => $existingThumbnail['url'],
                    'local_path' => $existingThumbnail['path'],
                    'file_exists' => true
                ];
            }

            // Generate unique filename for thumbnail - always use image extension
            $extension = $this->getThumbnailExtension($thumbnailUrl);
            $filename = $instagramId . '_thumb_' . uniqid() . '.' . $extension;

            // Create yearly/monthly path
            $year = date('Y');
            $month = date('m');
            $relativePath = "$year/$month/$filename";
            $fullPath = $this->uploadDir . "/$year/$month/$filename";

            // Download the thumbnail
            $fileContent = $this->downloadFile($thumbnailUrl);

            if ($fileContent === false) {
                return [
                    'success' => false,
                    'error' => 'Failed to download thumbnail'
                ];
            }

            // Save the thumbnail
            if (file_put_contents($fullPath, $fileContent) === false) {
                return [
                    'success' => false,
                    'error' => 'Failed to save thumbnail'
                ];
            }

            return [
                'success' => true,
                'local_url' => $this->baseUrl . '/' . $relativePath,
                'local_path' => $fullPath,
                'file_exists' => false
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    private function findExistingFile($instagramId, $mediaType = 'image', $isThumbnail = false) {
        // Search through all year/month directories for files matching this Instagram ID
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($this->uploadDir, \RecursiveDirectoryIterator::SKIP_DOTS)
        );

        $prefix = $isThumbnail ? $instagramId . '_thumb_' : $instagramId . '_';

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $filename = $file->getFilename();

                // Check if filename starts with the Instagram ID prefix
                if (strpos($filename, $prefix) === 0) {
                    // Get relative path from upload directory
                    $relativePath = substr($file->getPath(), strlen($this->uploadDir) + 1);
                    $relativePath = str_replace('\\', '/', $relativePath) . '/' . $filename;

                    return [
                        'path' => $file->getPathname(),
                        'url' => $this->baseUrl . '/' . $relativePath,
                        'filename' => $filename
                    ];
                }
            }
        }

        return null;
    }

    private function downloadFile($url) {
        // Use cURL to download the file
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            return false;
        }

        return $result;
    }

    private function getFileExtension($url, $mediaType) {
        // Try to get extension from URL
        $path = parse_url($url, PHP_URL_PATH);
        if ($path) {
            $extension = pathinfo($path, PATHINFO_EXTENSION);
            if (!empty($extension)) {
                return strtolower($extension);
            }
        }

        // Fallback to media type
        switch ($mediaType) {
            case 'video':
                return 'mp4';
            case 'image':
            default:
                return 'jpg';
        }
    }

    private function getThumbnailExtension($url) {
        // For thumbnails, always prefer image extensions
        $path = parse_url($url, PHP_URL_PATH);
        if ($path) {
            $extension = pathinfo($path, PATHINFO_EXTENSION);
            if (!empty($extension)) {
                $extension = strtolower($extension);
                // If it's a video extension, convert to image
                if (in_array($extension, ['mp4', 'mov', 'avi', 'webm'])) {
                    return 'jpg';
                }
                return $extension;
            }
        }
        // Default thumbnail extension
        return 'jpg';
    }

    public function cleanupOldFiles($days = 30) {
        // This method can be used to clean up old files
        // For now, we'll keep all files
        return true;
    }

    public function getStorageInfo() {
        $totalSize = 0;
        $fileCount = 0;

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($this->uploadDir, \RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $totalSize += $file->getSize();
                $fileCount++;
            }
        }

        return [
            'total_files' => $fileCount,
            'total_size' => $totalSize,
            'total_size_mb' => round($totalSize / (1024 * 1024), 2),
            'upload_directory' => $this->uploadDir
        ];
    }
}