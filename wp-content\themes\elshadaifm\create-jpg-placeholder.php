<?php
// Create a simple JPG placeholder image
$width = 400;
$height = 400;

// Create image
$image = imagecreatetruecolor($width, $height);

// Colors
$purple = imagecolorallocate($image, 139, 92, 246); // #8B5CF6
$white = imagecolorallocate($image, 255, 255, 255);

// Fill background
imagefill($image, 0, 0, $purple);

// Add play button (triangle)
$triangle = array(
    $width/2 - 30, $height/2 - 40,  // top left
    $width/2 - 30, $height/2 + 40,  // bottom left  
    $width/2 + 40, $height/2         // right point
);
imagefilledpolygon($image, $triangle, 3, $white);

// Add text "VIDEO"
$font_size = 5;
$text = "VIDEO";
$text_width = imagefontwidth($font_size) * strlen($text);
$text_x = ($width - $text_width) / 2;
$text_y = $height/2 + 60;
imagestring($image, $font_size, $text_x, $text_y, $text, $white);

// Save as JPG
$output_path = 'wp-content/themes/elshadaifm/assets/images/video-placeholder.jpg';
imagejpeg($image, $output_path, 90);

// Clean up
imagedestroy($image);

echo "Video placeholder created at: " . $output_path . "\n";
?>
