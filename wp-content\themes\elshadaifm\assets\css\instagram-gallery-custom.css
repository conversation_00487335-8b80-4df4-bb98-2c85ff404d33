/* ==========================================================================
   INSTAGRAM GALLERY - CUSTOM THEME INTEGRATION
   Ensures perfect consistency with ElshadaiFM About Us page styling
   ========================================================================== */

/* Override plugin gallery container to match theme container */
.eig-gallery-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
    background: transparent;
}

/* Header consistency with about-us page */
.eig-gallery-header {
    text-align: center;
    max-width: 700px;
    margin: 0 auto 80px;
    padding: 0;
}

.eig-badge,
.about-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(139, 92, 246, 0.1);
    color: #8B5CF6;
    padding: 6px 16px;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 20px;
}

.eig-gallery-title,
.about-title {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 20px;
    color: #1e293b;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.eig-gallery-subtitle,
.about-subtitle {
    font-size: 1.1rem;
    color: #64748b;
    line-height: 1.6;
    margin: 0 0 40px 0;
}

/* Stats grid matching about-us highlights */
.eig-gallery-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.eig-stat-item {
    text-align: center;
    padding: 30px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(226, 232, 240, 0.6);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.eig-stat-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(139, 92, 246, 0.15);
    border-color: rgba(139, 92, 246, 0.3);
}

.eig-stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    color: #8B5CF6;
    line-height: 1;
    margin-bottom: 10px;
}

.eig-stat-label {
    display: block;
    font-size: 0.9rem;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

/* Gallery grid enhancements */
.eig-gallery-grid {
    display: grid;
    gap: 24px;
    margin-bottom: 60px;
}

.eig-columns-3 {
    grid-template-columns: repeat(3, 1fr);
}

/* Post items matching theme card style */
.eig-post-item {
    position: relative;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(226, 232, 240, 0.6);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
}

.eig-post-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(139, 92, 246, 0.15);
    border-color: rgba(139, 92, 246, 0.3);
}

/* Post info styling to match theme */
.eig-post-info {
    padding: 24px;
}

.eig-post-date {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #8B5CF6;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 12px;
}

.eig-post-caption {
    color: #64748b;
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Load more button matching theme CTA style */
.eig-load-more-btn {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    border: none;
    padding: 18px 36px;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
    position: relative;
    overflow: hidden;
    font-family: 'Inter', sans-serif;
}

.eig-load-more-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

.eig-load-more-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Modal styling to match theme */
.eig-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Inter', sans-serif;
}

.eig-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.eig-modal-content {
    position: relative;
    background: white;
    border-radius: 24px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    animation: modalOpen 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes modalOpen {
    from {
        opacity: 0;
        transform: scale(0.85) translateY(30px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.eig-modal-close {
    position: absolute;
    top: 24px;
    right: 24px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.eig-modal-close:hover {
    background: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
}

/* Modal content layout */
.eig-modal-post {
    display: flex;
    max-width: 1000px;
    max-height: 600px;
}

.eig-modal-media {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8fafc;
    min-height: 400px;
}

.eig-modal-details {
    flex: 0 0 350px;
    padding: 32px;
    display: flex;
    flex-direction: column;
    max-height: 600px;
    overflow-y: auto;
}

.eig-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e2e8f0;
}

.eig-modal-account {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #8B5CF6;
    font-weight: 600;
    font-size: 1.1rem;
}

.eig-modal-stats {
    display: flex;
    gap: 16px;
}

.eig-modal-stat {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #64748b;
    font-size: 0.9rem;
    font-weight: 500;
}

.eig-modal-caption {
    color: #1e293b;
    line-height: 1.7;
    margin-bottom: 24px;
    flex-grow: 1;
    font-size: 0.95rem;
}

.eig-modal-footer {
    margin-top: auto;
    padding-top: 24px;
    border-top: 1px solid #e2e8f0;
}

.eig-modal-date {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #64748b;
    font-size: 0.9rem;
    margin-bottom: 16px;
}

.eig-modal-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #8B5CF6;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 12px;
    background: rgba(139, 92, 246, 0.1);
}

.eig-modal-link:hover {
    color: #7C3AED;
    background: rgba(139, 92, 246, 0.15);
    transform: translateX(4px);
}

/* Video overlay styling - hidden for native video player */
.eig-video-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 3.5rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
    z-index: 2;
    display: none; /* Hidden for native video player */
}

.eig-post-item:hover .eig-video-overlay {
    transform: translate(-50%, -50%) scale(1.15);
    text-shadow: 0 6px 30px rgba(0, 0, 0, 0.7);
}

.eig-video-views {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(0, 0, 0, 0.75);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
    z-index: 3;
    backdrop-filter: blur(10px);
}

/* Post overlay with gradient matching theme colors */
.eig-post-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.85), rgba(124, 58, 237, 0.85));
    opacity: 0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    backdrop-filter: blur(2px);
}

.eig-post-item:hover .eig-post-overlay {
    opacity: 1;
}

.eig-post-stats {
    display: flex;
    gap: 24px;
    color: white;
}

.eig-stat {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 1.1rem;
}

.eig-stat i {
    font-size: 1.3rem;
}

/* Empty state styling */
.eig-empty-state {
    text-align: center;
    padding: 100px 20px;
    color: #64748b;
    background: #f8fafc;
    border-radius: 24px;
    border: 2px dashed #cbd5e1;
}

.eig-empty-icon {
    font-size: 5rem;
    color: #cbd5e1;
    margin-bottom: 30px;
}

.eig-empty-state h3 {
    font-size: 1.8rem;
    color: #1e293b;
    margin-bottom: 15px;
    font-weight: 700;
}

.eig-empty-state p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 30px;
}

/* Responsive design */
@media (max-width: 1024px) {
    .eig-columns-3 {
        grid-template-columns: repeat(2, 1fr);
    }

    .eig-gallery-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .eig-gallery-container {
        padding: 0 15px;
    }

    .eig-columns-3 {
        grid-template-columns: 1fr;
    }

    .eig-gallery-stats {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .eig-gallery-grid {
        gap: 20px;
    }

    .eig-modal-post {
        flex-direction: column;
        max-height: 90vh;
    }

    .eig-modal-details {
        flex: none;
        max-height: none;
        padding: 24px;
    }

    .eig-video-overlay {
        font-size: 2.5rem;
    }

    .eig-gallery-header {
        margin-bottom: 50px;
    }

    .eig-stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .eig-post-info {
        padding: 20px;
    }

    .eig-modal-content {
        max-width: 95vw;
        max-height: 95vh;
        border-radius: 20px;
    }

    .eig-modal-details {
        padding: 20px;
    }

    .eig-load-more-btn {
        padding: 16px 28px;
        font-size: 0.95rem;
    }

    .eig-post-stats {
        gap: 16px;
    }

    .eig-stat {
        font-size: 1rem;
    }
}

/* Animation enhancements */
.eig-post-item.eig-new-post {
    animation: slideInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(40px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Focus states for accessibility */
.eig-post-item:focus,
.eig-load-more-btn:focus,
.eig-modal-close:focus {
    outline: 3px solid #8B5CF6;
    outline-offset: 3px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .eig-post-overlay {
        background: rgba(0, 0, 0, 0.9);
    }

    .eig-badge {
        background: #8B5CF6;
        color: white;
    }

    .eig-modal-overlay {
        background: rgba(0, 0, 0, 0.95);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .eig-post-item,
    .eig-post-image,
    .eig-video-thumbnail,
    .eig-video-overlay,
    .eig-load-more-btn,
    .eig-modal-content {
        animation: none;
        transition: none;
    }

    .eig-post-item:hover {
        transform: none;
    }
}

/* Print styles */
@media print {
    .eig-modal,
    .eig-load-more-container {
        display: none !important;
    }

    .eig-post-overlay {
        display: none !important;
    }

    .eig-post-item {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* ==========================================================================
   NEW INSTAGRAM GALLERY STYLES - Direct Database Implementation
   ========================================================================== */

/* Gallery Controls */
.gallery-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    flex-wrap: wrap;
    gap: 20px;
}

.gallery-sort {
    display: flex;
    align-items: center;
    gap: 10px;
}

.gallery-sort label {
    font-weight: 600;
    color: #475569;
}

.sort-select {
    padding: 10px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #475569;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.sort-select:hover {
    border-color: #8B5CF6;
}

.sort-select:focus {
    outline: none;
    border-color: #8B5CF6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.gallery-stats {
    display: flex;
    align-items: center;
    gap: 10px;
}

.post-count {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
}

/* Instagram Grid */
.instagram-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.instagram-post-item {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
}

.instagram-post-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.instagram-post-media {
    position: relative;
    width: 100%;
    padding-bottom: 100%; /* 1:1 aspect ratio */
    overflow: hidden;
    background: transparent;
}

.instagram-post-media img,
.instagram-post-media video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    z-index: 1;
}

/* Image loading states and error handling */
.instagram-post-media::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    z-index: 0;
}

/* Fallback for broken images */
.instagram-post-media img[src=""],
.instagram-post-media img:not([src]),
.instagram-post-media img[src="data:,"],
.instagram-post-media img[error] {
    opacity: 0;
}

.instagram-post-media::after {
    content: '\f16d';
    font-family: 'Font Awesome 5 Brands';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3rem;
    color: #cbd5e1;
    z-index: 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* Show fallback icon when image fails to load */
.instagram-post-media.has-error::after {
    opacity: 1;
}


.instagram-post-item:hover .instagram-post-media img,
.instagram-post-item:hover .instagram-post-media video {
    transform: scale(1.05);
}

.video-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    z-index: 2;
}

/* Video thumbnail styling */
.video-thumbnail:hover .video-play-button {
    transform: translate(-50%, -50%) scale(1.1);
    background: rgba(139, 92, 246, 0.8);
}

.video-thumbnail:hover img {
    transform: scale(1.05);
}

.video-play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    transition: all 0.3s ease;
    z-index: 2;
}

.instagram-post-info {
    padding: 20px;
    background: white;
    border-top: 1px solid #f1f5f9;
}

.post-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.post-likes,
.post-comments {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
}

.post-likes i,
.post-comments i {
    color: #ef4444;
}

.post-caption {
    color: #475569;
    line-height: 1.6;
    margin-bottom: 15px;
    font-size: 14px;
}

.post-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.post-time {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 500;
}

.post-link {
    color: #8B5CF6;
    font-size: 16px;
    transition: all 0.3s ease;
}

.post-link:hover {
    color: #7C3AED;
    transform: scale(1.1);
}

/* Load More Button */
.gallery-load-more {
    text-align: center;
    margin-top: 50px;
}

.load-more-btn {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 4px 20px rgba(139, 92, 246, 0.3);
}

.load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(139, 92, 246, 0.4);
}

.load-more-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* No posts message */
.gallery-no-posts {
    text-align: center;
    padding: 80px 20px;
    color: #64748b;
    font-size: 18px;
    background: #f8fafc;
    border-radius: 16px;
    border: 2px dashed #cbd5e1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .gallery-controls {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .gallery-sort {
        justify-content: center;
    }

    .instagram-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .instagram-post-info {
        padding: 15px;
    }

    .post-stats {
        flex-direction: column;
        gap: 10px;
    }

    .load-more-btn {
        padding: 14px 28px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .instagram-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .instagram-post-item {
        max-width: 100%;
    }
}

/* Animation for new posts */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.instagram-post-item {
    animation: fadeInUp 0.6s ease-out;
}

.instagram-post-item:nth-child(1) { animation-delay: 0.1s; }
.instagram-post-item:nth-child(2) { animation-delay: 0.2s; }
.instagram-post-item:nth-child(3) { animation-delay: 0.3s; }
.instagram-post-item:nth-child(4) { animation-delay: 0.4s; }
.instagram-post-item:nth-child(5) { animation-delay: 0.5s; }
.instagram-post-item:nth-child(6) { animation-delay: 0.6s; }

/* Loading state */
.instagram-grid.loading {
    opacity: 0.5;
    pointer-events: none;
}

/* ==========================================================================
   GALLERY PAGE LAYOUT STYLING
   ========================================================================== */

/* Gallery page consistency styling */
.gallery-intro {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.instagram-gallery-section {
    padding: 80px 0;
    background: #ffffff;
}

.gallery-cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
}

.gallery-cta-section .cta-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;
    text-align: left;
}

.gallery-cta-section .cta-title {
    color: white;
    margin-bottom: 15px;
}

.gallery-cta-section .cta-subtitle {
    color: rgba(255, 255, 255, 0.9);
}

.gallery-cta-section .cta-button {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 16px 32px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    backdrop-filter: blur(10px);
}

.gallery-cta-section .cta-button:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    color: white;
}

/* Video.js Custom Styling for Instagram Gallery */
.instagram-post-media .video-js {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    overflow: hidden;
}

.instagram-post-media .video-js .vjs-tech {
    border-radius: 12px;
}

.instagram-post-media .vjs-control-bar {
    background: rgba(0, 0, 0, 0.7);
    border-radius: 0 0 12px 12px;
}

/* Video Container with Play Button Overlay */
.video-container {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
}

.video-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.play-button-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-container:hover .play-button-overlay {
    opacity: 1;
}

.play-button {
    width: 60px;
    height: 60px;
    background: rgba(139, 92, 246, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
}

.play-button:hover {
    background: rgba(139, 92, 246, 1);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(139, 92, 246, 0.6);
}

.play-button i {
    color: white;
    font-size: 20px;
    margin-left: 3px;
}

.video-views {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 2;
}

/* Responsive design for gallery sections */
@media (max-width: 768px) {
    .gallery-cta-section .cta-content {
        flex-direction: column;
        text-align: center;
    }

    .gallery-intro,
    .instagram-gallery-section,
    .gallery-cta-section {
        padding: 40px 0;
    }
}