/**
 * Instagram Gallery JavaScript
 *
 * @package Elshadaifm
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        var $grid = $('#instagram-gallery-grid');
        var $loadMoreBtn = $('#load-more-instagram');
        var $sortSelect = $('#sort-instagram');
        var $postCount = $('.post-count');
        var currentOffset = 12; // Initial posts loaded
        var currentSort = 'date_desc';
        var isLoading = false;

        // Handle video play button clicks
        function initializeVideoHandlers() {
            // Handle click on video containers
            $(document).on('click', '.video-container', function(e) {
                e.preventDefault();
                var $container = $(this);
                var videoUrl = $container.data('video-url');

                if (videoUrl) {
                    // Open video in new tab/window
                    window.open(videoUrl, '_blank');
                }
            });
        }

        // Handle image loading errors
        function handleImageErrors() {
            $grid.find('img').each(function() {
                var $img = $(this);
                var $mediaContainer = $img.closest('.instagram-post-media');

                // Handle image loading errors
                $img.on('error', function() {
                    console.log('Image error for:', $img.attr('src'));
                    $mediaContainer.addClass('has-error');

                    // Try to load thumbnail if available
                    var originalSrc = $img.attr('src');
                    var thumbnailSrc = $img.data('thumbnail');

                    if (thumbnailSrc && thumbnailSrc !== originalSrc) {
                        console.log('Trying thumbnail:', thumbnailSrc);
                        $img.attr('src', thumbnailSrc);
                    } else {
                        // Show Instagram icon as fallback
                        console.log('Using fallback icon');
                        $img.hide();
                        $mediaContainer.addClass('has-error');
                    }
                });

                // Handle successful image load
                $img.on('load', function() {
                    console.log('Image loaded successfully:', $img.attr('src'));
                    $mediaContainer.removeClass('has-error');
                    $img.show();
                });
            });
        }

        // Initialize video handlers
        initializeVideoHandlers();

        // Initialize error handling for existing images
        handleImageErrors();

        // Load more posts
        $loadMoreBtn.on('click', function(e) {
            e.preventDefault();

            if (isLoading) return;

            isLoading = true;
            $loadMoreBtn.find('.btn-text').hide();
            $loadMoreBtn.find('.btn-spinner').show();

            $.ajax({
                url: gallery_vars.ajax_url,
                type: 'POST',
                data: {
                    action: 'load_more_instagram_posts',
                    nonce: gallery_vars.nonce,
                    limit: 12,
                    offset: currentOffset,
                    sort: currentSort
                },
                success: function(response) {
                    if (response.success) {
                        // Append new posts
                        $grid.append(response.data.html);

                        // Initialize error handling for new images
                        setTimeout(handleImageErrors, 100);

                        // Update offset
                        currentOffset += 12;

                        // Hide load more button if no more posts
                        if (!response.data.has_more) {
                            $loadMoreBtn.hide();
                        }
                    } else {
                        console.error('Failed to load posts:', response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', error);
                },
                complete: function() {
                    isLoading = false;
                    $loadMoreBtn.find('.btn-text').show();
                    $loadMoreBtn.find('.btn-spinner').hide();
                }
            });
        });

        // Sort posts
        $sortSelect.on('change', function() {
            var newSort = $(this).val();

            if (isLoading || newSort === currentSort) return;

            isLoading = true;
            currentSort = newSort;
            currentOffset = 12; // Reset offset

            // Show loading state
            $grid.css('opacity', '0.5');

            $.ajax({
                url: gallery_vars.ajax_url,
                type: 'POST',
                data: {
                    action: 'sort_instagram_posts',
                    nonce: gallery_vars.nonce,
                    limit: currentOffset,
                    sort: currentSort
                },
                success: function(response) {
                    if (response.success) {
                        // Replace grid content
                        $grid.html(response.data.html);

                        // Initialize error handling for new images
                        setTimeout(handleImageErrors, 100);

                        // Show/hide load more button
                        if (response.data.has_more) {
                            $loadMoreBtn.show();
                        } else {
                            $loadMoreBtn.hide();
                        }
                    } else {
                        console.error('Failed to sort posts:', response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', error);
                },
                complete: function() {
                    isLoading = false;
                    $grid.css('opacity', '1');
                }
            });
        });

        // Initialize - hide load more if less than initial posts
        if (currentOffset >= parseInt($postCount.text())) {
            $loadMoreBtn.hide();
        }

        // Make post info always visible (no hover effects needed)
        $('.instagram-post-item').find('.instagram-post-info').show();

        // Force load video thumbnails
        $('.video-container img').each(function() {
            var $img = $(this);
            var src = $img.attr('src');
            if (src) {
                // Force reload the image
                $img.attr('src', src + '?t=' + new Date().getTime());
            }
        });

    });

})(jQuery);