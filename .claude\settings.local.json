{"permissions": {"allow": ["<PERSON><PERSON>(move:*)", "Bash(php:*)", "<PERSON><PERSON>(curl:*)", "WebFetch(domain:cdn.jsdelivr.net)", "<PERSON><PERSON>(dir:*)", "mcp__mcpMySql__execute_query", "Bash(wp-contentthemeselshadaifmassetscssbroadcast-schedule.css)", "Bash(wp plugin status:*)", "WebFetch(domain:ai.google.dev)", "<PERSON><PERSON>(Select-String:*)", "WebFetch(domain:platform.openai.com)", "WebFetch(domain:www.elshaddaifm.id)", "WebFetch(domain:localhost)", "WebFetch(domain:www.google.com)", "<PERSON><PERSON>(mkdir:*)", "Bash(copy:*)", "Bash(find:*)", "Ba<PERSON>(wp:*)", "Bash(git add:*)", "Bash(git commit:*)", "WebFetch(domain:docs.apify.com)", "WebSearch", "Bash(/dev/null)", "Ba<PERSON>([:*)", "<PERSON><PERSON>(mysql:*)", "<PERSON><PERSON>(findstr:*)", "Bash(rm:*)", "<PERSON><PERSON>(rmdir:*)", "Bash(tree:*)", "Bash(git rm:*)", "Bash(bash:*)", "WebFetch(domain:cdn.plyr.io)"], "deny": [], "ask": []}}