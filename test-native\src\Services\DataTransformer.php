<?php
namespace Services;

class DataTransformer {
    private $mediaDownloader;

    public function __construct(MediaDownloader $mediaDownloader = null) {
        $this->mediaDownloader = $mediaDownloader;
    }

    public function transformPost($item) {
        // Get original URLs
        $originalMediaUrl = $this->getBestMediaUrl($item);
        $originalThumbnailUrl = $this->getThumbnailUrl($item);
        $mediaType = $this->determineMediaType($item);

        // Download media files locally if MediaDownloader is available
        $localMediaUrl = $originalMediaUrl;
        $localThumbnailUrl = $originalThumbnailUrl;

        if ($this->mediaDownloader) {
            try {
                // Download main media
                $mediaResult = $this->mediaDownloader->downloadMedia(
                    $originalMediaUrl,
                    $item['id'] ?? uniqid(),
                    $mediaType
                );

                if ($mediaResult['success']) {
                    $localMediaUrl = $mediaResult['local_url'];
                }

                // Download thumbnail
                $thumbnailResult = $this->mediaDownloader->downloadThumbnail(
                    $originalThumbnailUrl,
                    $item['id'] ?? uniqid()
                );

                if ($thumbnailResult['success']) {
                    $localThumbnailUrl = $thumbnailResult['local_url'];
                }

            } catch (\Exception $e) {
                // Log error but continue with original URLs
                error_log("Media download failed for {$item['id']}: " . $e->getMessage());
            }
        }

        return [
            'instagram_id' => $item['id'] ?? '',
            'shortcode' => $item['shortCode'] ?? '',
            'caption' => $this->cleanCaption($item['caption'] ?? ''),
            'media_url' => $localMediaUrl,
            'thumbnail_url' => $localThumbnailUrl,
            'original_media_url' => $originalMediaUrl,
            'original_thumbnail_url' => $originalThumbnailUrl,
            'media_type' => $mediaType,
            'like_count' => intval($item['likesCount'] ?? 0),
            'comment_count' => intval($item['commentsCount'] ?? 0),
            'post_date' => $this->formatDate($item['timestamp'] ?? ''),
            'is_video' => $this->isVideo($item) ? 1 : 0,
            'video_view_count' => intval($item['videoViewCount'] ?? 0),
            'permalink' => $this->buildPermalink($item['shortCode'] ?? ''),
            'username' => $item['ownerUsername'] ?? '',
            'status' => 'active'
        ];
    }

    private function cleanCaption($caption) {
        if (empty($caption)) return '';
        $caption = trim($caption);
        $caption = preg_replace('/\s+/', ' ', $caption);
        if (strlen($caption) > 2000) {
            $caption = substr($caption, 0, 1997) . '...';
        }
        return $caption;
    }

    private function getBestMediaUrl($item) {
        if (isset($item['videoUrl']) && !empty($item['videoUrl'])) {
            return $item['videoUrl'];
        }
        if (isset($item['displayUrl']) && !empty($item['displayUrl'])) {
            return $item['displayUrl'];
        }
        return $item['url'] ?? '';
    }

    private function getThumbnailUrl($item) {
        if (isset($item['thumbnailSrc']) && !empty($item['thumbnailSrc'])) {
            return $item['thumbnailSrc'];
        }
        if (isset($item['displayUrl']) && !empty($item['displayUrl'])) {
            return $item['displayUrl'];
        }
        return $this->getBestMediaUrl($item);
    }

    private function determineMediaType($item) {
        if (isset($item['isVideo']) && $item['isVideo']) {
            return 'video';
        }
        if (isset($item['videoUrl']) && !empty($item['videoUrl'])) {
            return 'video';
        }
        return 'image';
    }

    private function isVideo($item) {
        return $this->determineMediaType($item) === 'video';
    }

    private function formatDate($timestamp) {
        if (empty($timestamp)) {
            return date('Y-m-d H:i:s');
        }
        if (is_numeric($timestamp)) {
            if (strlen($timestamp) > 10) {
                $timestamp = intval($timestamp / 1000);
            }
            return date('Y-m-d H:i:s', $timestamp);
        }
        $parsed = strtotime($timestamp);
        return $parsed !== false ? date('Y-m-d H:i:s', $parsed) : date('Y-m-d H:i:s');
    }

    private function buildPermalink($shortcode) {
        if (empty($shortcode)) return '';
        return 'https://www.instagram.com/p/' . $shortcode . '/';
    }

    public function isValidPost($item) {
        if (!isset($item['type']) || !in_array($item['type'], ['Video', 'Image', 'Post'])) {
            return false;
        }

        $transformed = $this->transformPost($item);
        if (empty($transformed['instagram_id']) || empty($transformed['media_url']) || empty($transformed['username'])) {
            return false;
        }

        return true;
    }
}