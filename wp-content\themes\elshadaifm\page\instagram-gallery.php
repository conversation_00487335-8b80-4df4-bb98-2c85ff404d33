<?php
/**
 * Template Name: Instagram Gallery
 *
 * @package Elshadaifm
 */

get_header();

// Load gallery-specific styles consistent with about-us page
$version = '1.0.0';
wp_enqueue_style( 'about-us-base', get_template_directory_uri() . '/assets/css/about-us/about-base.css', array(), $version );
wp_enqueue_style( 'instagram-gallery-custom', get_template_directory_uri() . '/assets/css/instagram-gallery-custom.css', array(), $version );

// Load hero section for gallery page
wp_enqueue_style( 'hero-section', get_template_directory_uri() . '/assets/css/hero-section.css', array(), '1.0.0' );
wp_enqueue_script( 'hero-section', get_template_directory_uri() . '/assets/js/hero-section.js', array(), '1.0.0', true );

// Load gallery functionality
wp_enqueue_script( 'instagram-gallery', get_template_directory_uri() . '/assets/js/instagram-gallery.js', array('jquery'), '1.0.0', true );
wp_localize_script( 'instagram-gallery', 'gallery_vars', array(
    'ajax_url' => admin_url( 'admin-ajax.php' ),
    'nonce' => wp_create_nonce( 'gallery_nonce' ),
    'initial_posts' => 12,
    'loading_text' => 'Loading...',
    'load_more_text' => 'Load More Posts',
    'template_url' => get_template_directory_uri()
));

// Include hero section template with gallery parameters
get_template_part( 'template-parts/hero-section', null, array(
    'video_id' => 'jnVRC97rKhU',
    'video_start' => 30,
    'title' => 'Galeri Instagram El-Shaddai FM',
    'subtitle' => 'Saksikan momen-momen istimewa pelayanan kami melalui foto dan video terbaru',
    'show_video' => true
) );
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">

        <!-- Introduction Section -->
        <section class="gallery-intro">
            <div class="about-container">
                <div class="about-content-wrapper">
                    <div class="about-header">
                        <div class="about-badge">Instagram Gallery</div>
                       
                    </div>

                </div>
            </div>
        </section>

        <!-- Instagram Gallery Section -->
        <section class="instagram-gallery-section">
            <div class="about-container">

              

                <!-- Gallery Grid -->
                <div id="instagram-gallery-grid" class="instagram-grid">
                    <?php
                    // Debug: Test database connection and URLs
                    global $wpdb;
                    $test_post = $wpdb->get_row("SELECT * FROM efm_eig_instagram_posts LIMIT 1");

                    if ($test_post) {
                        echo "<!-- Debug: Found " . $wpdb->num_rows . " posts -->\n";
                        echo "<!-- Debug: Sample media_url: " . $test_post->media_url . " -->\n";

                        // Test URL conversion
                        if (strpos($test_post->media_url, '/wp-content/') === 0) {
                            $converted_url = content_url(substr($test_post->media_url, 12));
                            echo "<!-- Debug: Converted URL: " . $converted_url . " -->\n";
                        }
                    } else {
                        echo "<!-- Debug: No posts found in database -->\n";
                    }

                    echo get_instagram_posts_html(12);
                    ?>
                </div>

                <!-- Load More Button -->
                <div class="gallery-load-more">
                    <button id="load-more-instagram" class="load-more-btn">
                        <span class="btn-text">Load More Posts</span>
                        <span class="btn-spinner" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    </button>
                </div>

            </div>
        </section>

        <!-- Call to Action Section -->
        <section class="gallery-cta-section">
            <div class="about-container">
                <div class="cta-content">
                    <div class="cta-text">
                        <h2 class="cta-title">Ikuti Kami di Instagram</h2>
                        <p class="cta-subtitle">
                            Jangan lewatkan update terbaru dari El-Shaddai FM. Follow akun Instagram resmi kami
                            untuk mendapatkan konten inspiratif setiap hari.
                        </p>
                    </div>
                    <div class="cta-action">
                        <a href="https://www.instagram.com/elshaddai_radio/" target="_blank" rel="noopener noreferrer" class="cta-button">
                            <i class="fab fa-instagram"></i> Follow @elshaddai_radio
                        </a>
                    </div>
                </div>
            </div>
        </section>

    </main><!-- #main -->
</div><!-- #primary -->

<?php
get_footer();